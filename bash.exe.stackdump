Stack trace:
Frame         Function      Args
0007FFFFA450  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9350) msys-2.0.dll+0x1FE8E
0007FFFFA450  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA728) msys-2.0.dll+0x67F9
0007FFFFA450  000210046832 (000210286019, 0007FFFFA308, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA450  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA450  000210068E24 (0007FFFFA460, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA730  00021006A225 (0007FFFFA460, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE598E0000 ntdll.dll
7FFE58650000 KERNEL32.DLL
7FFE56CC0000 KERNELBASE.dll
7FFE57F40000 USER32.dll
7FFE57650000 win32u.dll
7FFE58720000 GDI32.dll
7FFE56A30000 gdi32full.dll
7FFE57500000 msvcp_win.dll
7FFE56B70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE57840000 advapi32.dll
7FFE576E0000 msvcrt.dll
7FFE57D90000 sechost.dll
7FFE58C50000 RPCRT4.dll
7FFE56150000 CRYPTBASE.DLL
7FFE575B0000 bcryptPrimitives.dll
7FFE57F00000 IMM32.DLL
