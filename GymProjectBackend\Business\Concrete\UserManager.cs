﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Entities.Concrete;
using Core.Utilities.Business;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserManager : IUserService
    {
        IUserDal _userDal;

        public UserManager(IUserDal userDal)
        {
            _userDal = userDal;
        }
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        //buraya üye olurken girildiğinden secured operation aspect koyma
        public IResult Add(User user)
        {
            _userDal.Add(user);
            return new SuccessResult(Messages.UserAdded);
        }
        [PerformanceAspect(3)]
        //buraya üye olurken girildiğinden secured operation aspect koyma
        public List<OperationClaim> GetClaims(User user)
        {
            return _userDal.GetClaims(user);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int id)
        {
            _userDal.Delete(id);
            return new SuccessResult(Messages.UserDeleted);
        }
        [SecuredOperation("owner")]
        public IDataResult<List<User>> GetAll()
        {
            return new SuccessDataResult<List<User>>(_userDal.GetAll());
        }
        //buraya login olurken veri çekildiği için secured operation aspect koyma
        public User GetByMail(string email)
        {
            return _userDal.Get(u => u.Email == email);
        }
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        ////[SecuredOperation("owner")]
        //[ValidationAspect(typeof(UserValidator))]
        public IResult Update(User user)
        {
            // Önce eski User bilgilerini al
            var oldUser = _userDal.Get(u => u.UserID == user.UserID);

            // User tablosunu güncelle
            _userDal.Update(user);

            // Eğer email, firstName veya lastName değişmişse CompanyUser tablosunu da güncelle
            if (oldUser != null &&
                (oldUser.Email != user.Email ||
                 oldUser.FirstName != user.FirstName ||
                 oldUser.LastName != user.LastName))
            {
                SyncCompanyUserData(user, oldUser);
            }

            return new SuccessResult(Messages.UserUpdated);
        }

        //[SecuredOperation("owner,admin")]
        public IDataResult<User> GetById(int userId)
        {
            var user = _userDal.Get(u => u.UserID == userId && u.IsActive);
            if (user == null)
            {
                return new ErrorDataResult<User>(Messages.UserNotFound);
            }
            return new SuccessDataResult<User>(user);
        }

        /// <summary>
        /// User tablosu güncellendiğinde CompanyUser tablosundaki ilgili kayıtları senkronize eder
        /// </summary>
        private void SyncCompanyUserData(User updatedUser, User oldUser)
        {
            try
            {
                using (GymContext context = new GymContext())
                {
                    // Bu User'a ait CompanyUser kayıtlarını bul
                    // Email ile eşleşen CompanyUser'ları bul (çünkü User.Email = CompanyUser.Email)
                    var companyUsers = context.CompanyUsers
                        .Where(cu => cu.Email == oldUser.Email && cu.IsActive == true)
                        .ToList();

                    // CompanyUser kayıtlarını güncelle - SQL ile direkt güncelleme
                    string newEmail = updatedUser.Email;
                    string newFullName = $"{updatedUser.FirstName} {updatedUser.LastName}".Trim();

                    foreach (var companyUser in companyUsers)
                    {
                        bool emailChanged = companyUser.Email != newEmail;
                        bool nameChanged = companyUser.Name != newFullName;

                        if (emailChanged || nameChanged)
                        {
                            // SQL ile direkt güncelleme yap - Entity Framework context'ini hiç kullanma
                            string updateSql = @"
                                UPDATE CompanyUsers
                                SET Email = @Email,
                                    Name = @Name,
                                    UpdatedDate = @UpdatedDate
                                WHERE CompanyUserID = @CompanyUserID";

                            context.Database.ExecuteSqlRaw(updateSql,
                                new Microsoft.Data.SqlClient.SqlParameter("@Email", newEmail),
                                new Microsoft.Data.SqlClient.SqlParameter("@Name", newFullName),
                                new Microsoft.Data.SqlClient.SqlParameter("@UpdatedDate", DateTime.Now),
                                new Microsoft.Data.SqlClient.SqlParameter("@CompanyUserID", companyUser.CompanyUserID));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't throw to avoid breaking the main User update
                // Bu hata ana User güncelleme işlemini bozmasın diye sadece log'la
                System.Diagnostics.Debug.WriteLine($"CompanyUser sync error: {ex.Message}");
            }
        }

    }
}
