Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD713E0000 ntdll.dll
7FFD6FCA0000 KERNEL32.DLL
7FFD6E670000 KERNELBASE.dll
7FFD6F6F0000 USER32.dll
7FFD6EA60000 win32u.dll
7FFD70AE0000 GDI32.dll
7FFD6EFA0000 gdi32full.dll
7FFD6E530000 msvcp_win.dll
7FFD6EB50000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD71260000 advapi32.dll
7FFD6F480000 msvcrt.dll
7FFD6F550000 sechost.dll
7FFD70660000 RPCRT4.dll
7FFD6DC50000 CRYPTBASE.DLL
7FFD6F0E0000 bcryptPrimitives.dll
7FFD70110000 IMM32.DLL
