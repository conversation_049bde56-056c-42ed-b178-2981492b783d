﻿using Business.Abstract;
using Business.ValidationRules.FluentValidation;
using Core.Entities.Concrete;
using Entities.Concrete;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserController : ControllerBase
    {
        IUserService _userService;
        IMemberService _memberService;
        IProfileService _profileService;
        IAdvancedRateLimitService _advancedRateLimitService;
        IUserLicenseService _userLicenseService;

        public UserController(IUserService userService, IMemberService memberService, IProfileService profileService, IAdvancedRateLimitService advancedRateLimitService, IUserLicenseService userLicenseService)
        {
            _userService = userService;
            _memberService = memberService;
            _profileService = profileService;
            _advancedRateLimitService = advancedRateLimitService;
            _userLicenseService = userLicenseService;
        }
        [HttpGet("getall")]
        [Authorize]
        public IActionResult GetAll()
        {
            var result = _userService.GetAll();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("profile")]
        [Authorize]
        public IActionResult GetUserProfile()
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var userResult = _userService.GetById(userId);

            if (!userResult.Success)
            {
                return BadRequest(new { success = false, message = userResult.Message });
            }

            // Kullanıcıya bağlı üyelik bilgilerini getir
            var memberResult = _memberService.GetMemberByUserId(userId);

            // Kullanıcının rollerini kontrol et ve admin ise lisans bilgilerini getir
            var userRoles = _userLicenseService.GetUserRoles(userId);
            object licenseInfo = null;

            if (userRoles.Success && userRoles.Data.Contains("admin"))
            {
                var activeLicenses = _userLicenseService.GetMyActiveLicenses(userId);
                if (activeLicenses.Success && activeLicenses.Data.Any())
                {
                    var adminLicense = activeLicenses.Data.FirstOrDefault(l => l.Role == "admin");
                    if (adminLicense != null)
                    {
                        licenseInfo = new
                        {
                            packageName = adminLicense.PackageName,
                            endDate = adminLicense.EndDate,
                            remainingDays = adminLicense.RemainingDays,
                            isActive = adminLicense.IsActive
                        };
                    }
                }
            }

            return Ok(new {
                success = true,
                data = new {
                    user = new {
                        firstName = userResult.Data.FirstName,
                        lastName = userResult.Data.LastName,
                        email = userResult.Data.Email,
                        profileImagePath = userResult.Data.ProfileImagePath
                    },
                    member = memberResult.Success ? memberResult.Data : null,
                    license = licenseInfo
                }
            });
        }
        //[HttpPost("add")]
        //public IActionResult Add(User user)
        //{
        //    var result = _userService.Add(user);
        //    if (result.Success)
        //    {
        //        return Ok(result);
        //    }
        //    return BadRequest();

        //}
        //[HttpDelete("delete")]
        //public IActionResult Delete(int id)
        //{
        //    var result = _userService.Delete(id);
        //    if (result.Success)
        //    {
        //        return Ok(result);
        //    }
        //    return BadRequest();
        //}
        //[HttpPost("update")]
        //public IActionResult Update(User user)
        //{
        //    var result = _userService.Update(user);
        //    if (result.Success)
        //    {
        //        return Ok(result);
        //    }
        //    return BadRequest();
        //}

        [HttpPost("upload-profile-image")]
        [Authorize]
        public IActionResult UploadProfileImage(IFormFile file)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var result = _profileService.UploadProfileImage(file, userId);

            if (result.Success)
            {
                return Ok(result);
            }

            // Rate limit hatası için özel response
            return BadRequest(new {
                success = false,
                message = result.Message
            });
        }

        [HttpDelete("delete-profile-image")]
        [Authorize]
        public IActionResult DeleteProfileImage()
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var result = _profileService.DeleteProfileImage(userId);

            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("profile-image/{userId}")]
        public IActionResult GetProfileImage(int userId)
        {
            var userResult = _userService.GetById(userId);
            if (!userResult.Success)
            {
                return NotFound();
            }

            if (string.IsNullOrEmpty(userResult.Data.ProfileImagePath))
            {
                return NotFound();
            }

            var imagePath = Path.Combine(@"C:\GymProject\Images", Path.GetFileName(userResult.Data.ProfileImagePath));

            if (!System.IO.File.Exists(imagePath))
            {
                return NotFound();
            }

            var imageBytes = System.IO.File.ReadAllBytes(imagePath);
            var contentType = GetContentType(imagePath);

            return File(imageBytes, contentType);
        }

        [HttpGet("remaining-profile-image-uploads")]
        [Authorize]
        public IActionResult GetRemainingProfileImageUploads()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest(new { success = false, message = "Kullanıcı kimliği bulunamadı." });
            }

            var result = _advancedRateLimitService.GetRemainingProfileImageUploads(userId);
            if (result.Success)
            {
                return Ok(new {
                    success = true,
                    remainingUploads = result.Data,
                    maxUploadsPerDay = 3,
                    message = $"Bugün {result.Data} adet daha profil fotoğrafı yükleyebilirsiniz."
                });
            }

            return BadRequest(new { success = false, message = result.Message });
        }

        [HttpGet("remaining-file-downloads")]
        [Authorize]
        public IActionResult GetRemainingFileDownloads()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest(new { success = false, message = "Kullanıcı kimliği bulunamadı." });
            }

            var result = _advancedRateLimitService.GetRemainingFileDownloads(userId);
            if (result.Success)
            {
                return Ok(new {
                    success = true,
                    remainingDownloads = result.Data,
                    maxDownloadsPer10Minutes = 5,
                    message = $"10 dakika içinde {result.Data} adet daha dosya indirebilirsiniz."
                });
            }

            return BadRequest(new { success = false, message = result.Message });
        }

        [HttpPost("record-file-download")]
        [Authorize]
        public IActionResult RecordFileDownload()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return BadRequest(new { success = false, message = "Kullanıcı kimliği bulunamadı." });
            }

            // Rate limiting kontrolü
            var rateLimitCheck = _advancedRateLimitService.CheckFileDownloadAttempt(userId);
            if (!rateLimitCheck.Success)
            {
                return BadRequest(new { success = false, message = rateLimitCheck.Message });
            }

            // Başarılı download kaydı
            var recordResult = _advancedRateLimitService.RecordFileDownload(userId);
            if (recordResult.Success)
            {
                return Ok(new { success = true, message = "Dosya indirme kaydedildi." });
            }

            return BadRequest(new { success = false, message = recordResult.Message });
        }

        [HttpGet("health")]
        [AllowAnonymous]
        public IActionResult Health()
        {
            return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
        }

        private string GetContentType(string path)
        {
            var extension = Path.GetExtension(path).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                _ => "application/octet-stream"
            };
        }
    }
}
