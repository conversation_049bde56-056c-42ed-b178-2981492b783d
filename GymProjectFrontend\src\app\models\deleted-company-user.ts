export interface DeletedCompanyUser {
  // Temel salon bilgileri
  companyUserID: number;
  name: string;
  phoneNumber: string;
  email: string;
  cityName: string;
  townName: string;
  companyName: string;
  companyAddress: string;
  
  // Silme bilgileri
  deletedDate?: Date;
  deletedByUser: string;
  deletionReason: string;
  isRestored: boolean;
  restoredDate?: Date;
  restoredByUser?: string;
  
  // İstatistik bilgileri (silme anındaki durum)
  totalMembersAtDeletion: number;
  activeMembersAtDeletion: number;
  totalRevenueAtDeletion: number;
  totalPaymentsAtDeletion: number;
  unpaidTransactionsAtDeletion: number;
  
  // Geri getirme durumu
  canBeRestored: boolean;
  restoreBlockReason?: string;
  conflictWarnings: string[];
}

export interface CompanyUserDeletionSnapshot {
  companyUserID: number;
  deletionDate: Date;
  deletionReason: string;
  deletedByUserId: number;
  deletedByUserName: string;
  
  // Silinen veri sayıları
  statistics: DeletionStatistics;
  
  // Geri getirme için gerekli ID'ler
  deletedMemberIds: number[];
  deletedMembershipIds: number[];
  deletedPaymentIds: number[];
  deletedTransactionIds: number[];
  deletedEntryExitIds: number[];
  deletedDebtPaymentIds: number[];
  deletedCompanyExerciseIds: number[];
  deletedWorkoutProgramIds: number[];
  
  // Şirket bilgileri
  companyId?: number;
  companyAddressId?: number;
  userCompanyId?: number;
  userId?: number;
  
  // Çakışma kontrolü için
  potentialConflicts: ConflictInfo[];
}

export interface DeletionStatistics {
  totalMembers: number;
  totalMemberships: number;
  totalPayments: number;
  totalTransactions: number;
  totalEntryExits: number;
  totalDebtPayments: number;
  totalExercises: number;
  totalWorkoutPrograms: number;
  totalRevenue: number;
  unpaidAmount: number;
}

export interface ConflictInfo {
  entityType: string;
  conflictType: string;
  description: string;
  resolution: string;
  isBlocker: boolean;
}

export interface CompanyUserRestore {
  companyUserID: number;
  options: RestoreOptions;
  restoreReason: string;
  forceRestore: boolean;
}

export interface RestoreOptions {
  // Hangi veri türlerinin geri getirileceği
  restoreMembers: boolean;
  restoreMemberships: boolean;
  restorePayments: boolean;
  restoreTransactions: boolean;
  restoreEntryExitHistory: boolean;
  restoreDebtPayments: boolean;
  restoreExercises: boolean;
  restoreWorkoutPrograms: boolean;
  
  // Çakışma çözüm stratejileri
  memberConflictStrategy: ConflictResolutionStrategy;
  paymentConflictStrategy: ConflictResolutionStrategy;
  transactionConflictStrategy: ConflictResolutionStrategy;
  
  // Tarih aralığı filtresi
  restoreFromDate?: Date;
  restoreToDate?: Date;
}

export enum ConflictResolutionStrategy {
  Skip = 'Skip',
  Overwrite = 'Overwrite',
  CreateNew = 'CreateNew',
  Merge = 'Merge'
}

export interface CompanyUserRestoreResult {
  success: boolean;
  message: string;
  restoreDate: Date;
  restoredByUser: string;
  
  // Geri getirilen veri istatistikleri
  statistics: RestoreStatistics;
  
  // Çakışmalar ve çözümler
  conflictResolutions: ConflictResolution[];
  
  // Geri getirilemeyenler
  failedRestores: FailedRestore[];
}

export interface RestoreStatistics {
  restoredMembers: number;
  restoredMemberships: number;
  restoredPayments: number;
  restoredTransactions: number;
  restoredEntryExits: number;
  restoredDebtPayments: number;
  restoredExercises: number;
  restoredWorkoutPrograms: number;
  skippedDueToConflicts: number;
  failedRestores: number;
}

export interface ConflictResolution {
  entityType: string;
  entityId: number;
  conflictType: string;
  strategy: ConflictResolutionStrategy;
  resolution: string;
}

export interface FailedRestore {
  entityType: string;
  entityId: number;
  reason: string;
  details: string;
}
