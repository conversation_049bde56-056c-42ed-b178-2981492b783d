import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { 
  DeletedCompanyUser, 
  CompanyUserDeletionSnapshot, 
  CompanyUserRestore, 
  CompanyUserRestoreResult,
  ConflictInfo,
  RestoreOptions
} from '../models/deleted-company-user';
import { ListResponseModel, ResponseModel, SingleResponseModel } from '../models/responseModel';

@Injectable({
  providedIn: 'root'
})
export class DeletedCompanyUserService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  /**
   * Silinen tüm salonları getirir
   */
  getDeletedCompanyUsers(): Observable<ListResponseModel<DeletedCompanyUser>> {
    return this.httpClient.get<ListResponseModel<DeletedCompanyUser>>(
      this.apiUrl + 'deletedcompanyuser/getall'
    );
  }

  /**
   * <PERSON><PERSON><PERSON> bir silinen salonun detaylarını getirir
   */
  getDeletedCompanyUserById(companyUserId: number): Observable<SingleResponseModel<DeletedCompanyUser>> {
    return this.httpClient.get<SingleResponseModel<DeletedCompanyUser>>(
      this.apiUrl + 'deletedcompanyuser/getbyid?companyUserId=' + companyUserId
    );
  }

  /**
   * Silinen salon için deletion snapshot'ını getirir
   */
  getDeletionSnapshot(companyUserId: number): Observable<SingleResponseModel<CompanyUserDeletionSnapshot>> {
    return this.httpClient.get<SingleResponseModel<CompanyUserDeletionSnapshot>>(
      this.apiUrl + 'deletedcompanyuser/getsnapshot?companyUserId=' + companyUserId
    );
  }

  /**
   * Geri getirme öncesi çakışma analizi yapar
   */
  analyzeRestoreConflicts(companyUserId: number): Observable<ListResponseModel<ConflictInfo>> {
    return this.httpClient.get<ListResponseModel<ConflictInfo>>(
      this.apiUrl + 'deletedcompanyuser/analyzeconflicts?companyUserId=' + companyUserId
    );
  }

  /**
   * Salon geri getirme işlemini gerçekleştirir
   */
  restoreCompanyUser(restoreData: CompanyUserRestore): Observable<SingleResponseModel<CompanyUserRestoreResult>> {
    return this.httpClient.post<SingleResponseModel<CompanyUserRestoreResult>>(
      this.apiUrl + 'deletedcompanyuser/restore',
      restoreData
    );
  }

  /**
   * Silinen salonları tarih aralığına göre filtreler
   */
  getDeletedCompanyUsersByDateRange(startDate: Date, endDate: Date): Observable<ListResponseModel<DeletedCompanyUser>> {
    const params = new HttpParams()
      .set('startDate', startDate.toISOString())
      .set('endDate', endDate.toISOString());

    return this.httpClient.get<ListResponseModel<DeletedCompanyUser>>(
      this.apiUrl + 'deletedcompanyuser/getbydaterange',
      { params }
    );
  }

  /**
   * Belirli bir kullanıcının sildiği salonları getirir
   */
  getDeletedCompanyUsersByUser(userId: number): Observable<ListResponseModel<DeletedCompanyUser>> {
    return this.httpClient.get<ListResponseModel<DeletedCompanyUser>>(
      this.apiUrl + 'deletedcompanyuser/getbyuser?userId=' + userId
    );
  }

  /**
   * Geri getirilen salonları getirir
   */
  getRestoredCompanyUsers(): Observable<ListResponseModel<DeletedCompanyUser>> {
    return this.httpClient.get<ListResponseModel<DeletedCompanyUser>>(
      this.apiUrl + 'deletedcompanyuser/getrestored'
    );
  }

  /**
   * Toplu geri getirme işlemi
   */
  bulkRestore(companyUserIds: number[], options: RestoreOptions): Observable<ListResponseModel<CompanyUserRestoreResult>> {
    const requestData = {
      companyUserIds: companyUserIds,
      options: options
    };

    return this.httpClient.post<ListResponseModel<CompanyUserRestoreResult>>(
      this.apiUrl + 'deletedcompanyuser/bulkrestore',
      requestData
    );
  }

  /**
   * Kalıcı silme işlemi (dikkatli kullanılmalı)
   */
  permanentDelete(companyUserId: number, reason: string): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      this.apiUrl + 'deletedcompanyuser/permanentdelete?companyUserId=' + companyUserId,
      { body: reason }
    );
  }

  /**
   * Geri getirme işlemi için varsayılan seçenekleri döndürür
   */
  getDefaultRestoreOptions(): RestoreOptions {
    return {
      restoreMembers: true,
      restoreMemberships: true,
      restorePayments: true,
      restoreTransactions: true,
      restoreEntryExitHistory: true,
      restoreDebtPayments: true,
      restoreExercises: true,
      restoreWorkoutPrograms: true,
      memberConflictStrategy: 'Skip' as any,
      paymentConflictStrategy: 'Skip' as any,
      transactionConflictStrategy: 'Skip' as any
    };
  }

  /**
   * Silinen salon için özet bilgi getirir (dashboard için)
   */
  getDeletedCompanyUsersSummary(): Observable<any> {
    return this.httpClient.get<any>(
      this.apiUrl + 'deletedcompanyuser/getsummary'
    );
  }
}
