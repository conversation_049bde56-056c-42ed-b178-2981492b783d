﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Aspects.Autofac.Transaction;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyUserManager : ICompanyUserService
    {
        ICompanyUserDal _companyUserDal;
        IUserService _userService;
        ICompanyService _companyService;
        ICompanyAdressService _companyAdressService;
        IMemberService _memberService;

        public CompanyUserManager(ICompanyUserDal companyUserDal, IUserService userService, ICompanyService companyService, ICompanyAdressService companyAdressService, IMemberService memberService)
        {
            _companyUserDal = companyUserDal;
            _userService = userService;
            _companyService = companyService;
            _companyAdressService = companyAdressService;
            _memberService = memberService;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("CompanyUser")]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult Add(CompanyUser companyUser)
        {
            _companyUserDal.Add(companyUser);
            return new SuccessResult(Messages.UserAdded);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [SmartCacheRemoveAspect("CompanyUser")]
        public IResult Delete(int id)
        {
            _companyUserDal.Delete(id);
            return new SuccessResult(Messages.UserDeleted);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "Master")]
        public IDataResult<List<CompanyUser>> GetAll()
        {
            return new SuccessDataResult<List<CompanyUser>>(_companyUserDal.GetAll(), Messages.CompanyUserGetAll);
        }
        [SecuredOperation("owner")]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "City")]
        public IDataResult<List<CompanyUser>> GetByCityId(int cityId)
        {
            return new SuccessDataResult<List<CompanyUser>>(_companyUserDal.GetAll(c => c.CityID == cityId));

        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "Details")]
        public IDataResult<List<CompanyUserDetailDto>> GetCompanyUserDetails()
        {
            return new SuccessDataResult<List<CompanyUserDetailDto>>(_companyUserDal.GetCompanyUserDetails());
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "CompanyDetails")]
        public IDataResult<List<CompanyDetailDto>> GetCompanyDetails()
        {
            return new SuccessDataResult<List<CompanyDetailDto>>(_companyUserDal.GetCompanyDetails());
        }
        [SecuredOperation("owner")]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "CityDetails")]
        public IDataResult<List<CompanyDetailDto>> GetCompanyUserDetailsByCityId(int cityId)
        {
            return new SuccessDataResult<List<CompanyDetailDto>>(_companyUserDal.GetCompanyUserDetailsByCityId(cityId));
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("CompanyUser")]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult Update(CompanyUser companyUser)
        {
            _companyUserDal.Update(companyUser);
            return new SuccessResult(Messages.UserUpdated);
        }

        // Yeni eklenen metodlar
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "CompanyUser", "ById")]
        public IDataResult<CompanyUser> GetById(int companyUserID)
        {
            var companyUser = _companyUserDal.Get(cu => cu.CompanyUserID == companyUserID);
            if (companyUser == null)
            {
                return new ErrorDataResult<CompanyUser>("Şirket kullanıcısı bulunamadı");
            }
            return new SuccessDataResult<CompanyUser>(companyUser);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "CompanyUser", "FullDetails")]
        public IDataResult<CompanyUserFullDetailDto> GetCompanyUserFullDetails(int companyUserID)
        {
            var result = _companyUserDal.GetCompanyUserFullDetails(companyUserID);
            if (result != null)
            {
                return new SuccessDataResult<CompanyUserFullDetailDto>(result);
            }
            return new ErrorDataResult<CompanyUserFullDetailDto>("Kullanıcı detayları bulunamadı");
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "CompanyUser", "Paginated")]
        public IDataResult<PaginatedCompanyUserDto> GetCompanyUsersPaginated(int pageNumber, int pageSize, string searchTerm = "")
        {
            var result = _companyUserDal.GetCompanyUsersPaginated(pageNumber, pageSize, searchTerm);
            if (result != null)
            {
                return new SuccessDataResult<PaginatedCompanyUserDto>(result);
            }
            return new ErrorDataResult<PaginatedCompanyUserDto>("Sayfalanmış veriler alınamadı");
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect] // Çoklu tablo güncellemesi için transaction
        [SmartCacheRemoveAspect("CompanyUser")]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult UpdateCompanyUserFull(CompanyUserFullUpdateDto updateDto)
        {
            try
            {
                // 1. Mevcut CompanyUser kaydını al
                var existingCompanyUser = _companyUserDal.Get(cu => cu.CompanyUserID == updateDto.CompanyUserID);
                if (existingCompanyUser == null)
                {
                    return new ErrorResult("Şirket kullanıcısı bulunamadı");
                }

                string oldEmail = existingCompanyUser.Email;
                string oldName = existingCompanyUser.Name;

                // 2. CompanyUser tablosunu güncelle
                existingCompanyUser.Name = updateDto.Name;
                existingCompanyUser.PhoneNumber = updateDto.PhoneNumber;
                existingCompanyUser.Email = updateDto.Email;
                existingCompanyUser.CityID = updateDto.CityID;
                existingCompanyUser.TownID = updateDto.TownID;
                existingCompanyUser.IsActive = updateDto.IsActive;
                existingCompanyUser.UpdatedDate = DateTime.Now;

                _companyUserDal.Update(existingCompanyUser);

                // 3. Email veya isim değişmişse User tablosunu güncelle
                bool emailChanged = oldEmail != updateDto.Email;
                bool nameChanged = oldName != updateDto.Name;

                if (emailChanged || nameChanged)
                {
                    var existingUser = _userService.GetByMail(oldEmail);
                    if (existingUser != null)
                    {
                        // Email güncelle
                        if (emailChanged)
                        {
                            existingUser.Email = updateDto.Email;
                        }

                        // İsim güncelle
                        if (nameChanged)
                        {
                            var (firstName, lastName) = ParseFullName(updateDto.Name);
                            existingUser.FirstName = firstName;
                            existingUser.LastName = lastName;
                        }

                        existingUser.UpdatedDate = DateTime.Now;
                        _userService.Update(existingUser);
                    }
                }

                // 4. Company bilgileri güncellenmişse Company tablosunu güncelle
                if (updateDto.CompanyDataChanged && !string.IsNullOrEmpty(updateDto.CompanyName))
                {
                    // CompanyUser'dan Company'yi bul (UserCompanies tablosu üzerinden)
                    using (var context = new GymContext())
                    {
                        var userCompany = context.UserCompanies
                            .FirstOrDefault(uc => uc.UserID == updateDto.CompanyUserID && uc.IsActive == true);

                        if (userCompany != null)
                        {
                            // Company tablosunu güncelle
                            var existingCompany = _companyService.GetById(userCompany.CompanyId);
                            if (existingCompany.Success && existingCompany.Data != null)
                            {
                                var company = existingCompany.Data;
                                company.CompanyName = updateDto.CompanyName;
                                company.PhoneNumber = updateDto.CompanyPhone;
                                company.UpdatedDate = DateTime.Now;

                                _companyService.Update(company);
                            }

                            // CompanyAddress tablosunu güncelle
                            var companyAddresses = _companyAdressService.GetAll();
                            if (companyAddresses.Success && companyAddresses.Data != null)
                            {
                                var existingAddress = companyAddresses.Data
                                    .FirstOrDefault(ca => ca.CompanyID == userCompany.CompanyId && ca.IsActive == true);

                                if (existingAddress != null)
                                {
                                    existingAddress.Adress = updateDto.CompanyAddress;
                                    existingAddress.CityID = updateDto.CompanyCityID ?? existingAddress.CityID;
                                    existingAddress.TownID = updateDto.CompanyTownID ?? existingAddress.TownID;
                                    existingAddress.UpdatedDate = DateTime.Now;

                                    _companyAdressService.Update(existingAddress);
                                }
                            }
                        }
                    }
                }

                return new SuccessResult("Şirket kullanıcısı ve şirket bilgileri başarıyla güncellendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Güncelleme sırasında hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// İsmi FirstName ve LastName olarak ayırır
        /// Son kelimeyi LastName, geri kalanını FirstName olarak alır
        /// </summary>
        private (string FirstName, string LastName) ParseFullName(string fullName)
        {
            if (string.IsNullOrWhiteSpace(fullName))
            {
                return ("", "");
            }

            fullName = fullName.Trim();
            fullName = System.Text.RegularExpressions.Regex.Replace(fullName, @"\s+", " ");

            string[] nameParts = fullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            if (nameParts.Length == 0)
            {
                return ("", "");
            }
            else if (nameParts.Length == 1)
            {
                return (nameParts[0], "");
            }
            else
            {
                string lastName = nameParts[nameParts.Length - 1];
                string firstName = string.Join(" ", nameParts.Take(nameParts.Length - 1));
                return (firstName, lastName);
            }
        }
    }
}
