using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Silinen salon bilgilerini içeren DTO
    /// </summary>
    public class DeletedCompanyUserDto : IDto
    {
        // Temel salon bilgileri
        public int CompanyUserID { get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public string CityName { get; set; }
        public string TownName { get; set; }
        public string CompanyName { get; set; }
        public string CompanyAddress { get; set; }
        
        // Silme bilgileri
        public DateTime? DeletedDate { get; set; }
        public string DeletedByUser { get; set; }
        public string DeletionReason { get; set; }
        public bool IsRestored { get; set; }
        public DateTime? RestoredDate { get; set; }
        public string RestoredByUser { get; set; }
        
        // İstatistik bilgileri (silme anındaki durum)
        public int TotalMembersAtDeletion { get; set; }
        public int ActiveMembersAtDeletion { get; set; }
        public decimal TotalRevenueAtDeletion { get; set; }
        public int TotalPaymentsAtDeletion { get; set; }
        public int UnpaidTransactionsAtDeletion { get; set; }
        
        // Geri getirme durumu
        public bool CanBeRestored { get; set; }
        public string RestoreBlockReason { get; set; }
        public List<string> ConflictWarnings { get; set; } = new List<string>();
    }
}
