using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Salon geri getirme işlemi için DTO
    /// </summary>
    public class CompanyUserRestoreDto : IDto
    {
        public int CompanyUserID { get; set; }
        public RestoreOptions Options { get; set; } = new RestoreOptions();
        public string RestoreReason { get; set; }
        public bool ForceRestore { get; set; } = false; // Çakışmaları görmezden gel
    }
    
    public class RestoreOptions
    {
        // Hangi veri türlerinin geri getirileceği
        public bool RestoreMembers { get; set; } = true;
        public bool RestoreMemberships { get; set; } = true;
        public bool RestorePayments { get; set; } = true;
        public bool RestoreTransactions { get; set; } = true;
        public bool RestoreEntryExitHistory { get; set; } = true;
        public bool RestoreDebtPayments { get; set; } = true;
        public bool RestoreExercises { get; set; } = true;
        public bool RestoreWorkoutPrograms { get; set; } = true;
        
        // Çakışma çözüm stratejileri
        public ConflictResolutionStrategy MemberConflictStrategy { get; set; } = ConflictResolutionStrategy.Skip;
        public ConflictResolutionStrategy PaymentConflictStrategy { get; set; } = ConflictResolutionStrategy.Skip;
        public ConflictResolutionStrategy TransactionConflictStrategy { get; set; } = ConflictResolutionStrategy.Skip;
        
        // Tarih aralığı filtresi
        public DateTime? RestoreFromDate { get; set; }
        public DateTime? RestoreToDate { get; set; }
    }
    
    public enum ConflictResolutionStrategy
    {
        Skip,           // Çakışan kayıtları atla
        Overwrite,      // Mevcut kayıtların üzerine yaz
        CreateNew,      // Yeni ID ile oluştur
        Merge           // Mümkünse birleştir
    }
    
    /// <summary>
    /// Geri getirme işlemi sonucu
    /// </summary>
    public class CompanyUserRestoreResultDto : IDto
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public DateTime RestoreDate { get; set; }
        public string RestoredByUser { get; set; }
        
        // Geri getirilen veri istatistikleri
        public RestoreStatistics Statistics { get; set; } = new RestoreStatistics();
        
        // Çakışmalar ve çözümler
        public List<ConflictResolution> ConflictResolutions { get; set; } = new List<ConflictResolution>();
        
        // Geri getirilemeyenler
        public List<FailedRestore> FailedRestores { get; set; } = new List<FailedRestore>();
    }
    
    public class RestoreStatistics
    {
        public int RestoredMembers { get; set; }
        public int RestoredMemberships { get; set; }
        public int RestoredPayments { get; set; }
        public int RestoredTransactions { get; set; }
        public int RestoredEntryExits { get; set; }
        public int RestoredDebtPayments { get; set; }
        public int RestoredExercises { get; set; }
        public int RestoredWorkoutPrograms { get; set; }
        
        public int SkippedDueToConflicts { get; set; }
        public int FailedRestores { get; set; }
    }
    
    public class ConflictResolution
    {
        public string EntityType { get; set; }
        public int EntityId { get; set; }
        public string ConflictType { get; set; }
        public ConflictResolutionStrategy Strategy { get; set; }
        public string Resolution { get; set; }
    }
    
    public class FailedRestore
    {
        public string EntityType { get; set; }
        public int EntityId { get; set; }
        public string Reason { get; set; }
        public string Details { get; set; }
    }
}
