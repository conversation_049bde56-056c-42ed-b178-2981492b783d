﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ICompanyUserDal:IEntityRepository<CompanyUser>
    {
        // Mevcut metodlar
        List<CompanyDetailDto> GetCompanyDetails();
        List<CompanyDetailDto> GetCompanyUserDetailsByCityId(int cityId);
        List<CompanyUserDetailDto> GetCompanyUserDetails();

        // Yeni eklenen metodlar
        CompanyUserFullDetailDto GetCompanyUserFullDetails(int companyUserID);
        PaginatedCompanyUserDto GetCompanyUsersPaginated(int pageNumber, int pageSize, string searchTerm = "");
    }
}
