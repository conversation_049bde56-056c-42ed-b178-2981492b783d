﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDal : EfEntityRepositoryBase<User, GymContext>, IUserDal
    {
        public List<OperationClaim> GetClaims(User user)
        {
            using (var context = new GymContext())
            {
                var result = from OperationClaim in context.OperationClaims
                             join UserOperationClaim in context.UserOperationClaims
                             on OperationClaim.OperationClaimId equals UserOperationClaim.OperationClaimId
                             where UserOperationClaim.UserId == user.UserID
                             select new OperationClaim { OperationClaimId= OperationClaim.OperationClaimId, Name = OperationClaim.Name };
                return result.ToList();
            }
        }
       
    }
}
