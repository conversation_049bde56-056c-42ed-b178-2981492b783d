<div class="container-fluid mt-4 fade-in">
  <div
    class="d-flex justify-content-center align-items-center"
    *ngIf="isLoading"
    style="height: 100vh"
  >
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <!-- Main Content -->
    <div class="col-md-12">
      <div class="modern-card">
        <div class="card-header">
          <h5>Şirket Kullanıcıları</h5>
          <div class="d-flex align-items-center gap-3">
            <!-- <PERSON>lam Kullanıcı Sayısı -->
            <div class="total-members-badge">
              <span class="modern-badge modern-badge-primary">
                <i class="fas fa-building me-2"></i>
                Toplam: {{ totalCompanyUsers }} Kullanıcı
              </span>
            </div>
          </div>
        </div>

        <div class="card-body">
          <!-- Search and Filter -->
          <div class="row mb-4">
            <div class="col-md-8">
              <div class="search-input-container">
                <i class="fas fa-search search-icon"></i>
                <input
                  type="text"
                  class="search-input"
                  [(ngModel)]="searchText"
                  (input)="onSearch($event)"
                  placeholder="Ad, Soyad, Email veya Telefon ile arama yapın..."
                />
              </div>
            </div>
            <div class="col-md-4">
              <div class="d-flex justify-content-end">
                <div class="btn-group">
                  <button class="btn-modern btn-modern-outline" [class.active]="viewMode === 'table'" (click)="setViewMode('table')">
                    <i class="fas fa-table"></i> Tablo
                  </button>
                  <button class="btn-modern btn-modern-outline" [class.active]="viewMode === 'card'" (click)="setViewMode('card')">
                    <i class="fas fa-th-large"></i> Kart
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Active Filters -->
          <div class="filter-tags mb-4" *ngIf="searchText">
            <div class="filter-tag" *ngIf="searchText">
              <span>Arama: {{ searchText }}</span>
              <span class="remove-tag" (click)="clearSearch()">×</span>
            </div>
          </div>

          <!-- Table View -->
          <div class="table-responsive" *ngIf="viewMode === 'table'">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>Şirket ID</th>
                  <th>Ad Soyad</th>
                  <th>Email</th>
                  <th>Telefon</th>
                  <th>Şirket</th>
                  <th>Durum</th>
                  <th class="text-center">İşlem</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let companyUser of companyUsers" class="staggered-item">
                  <td>
                    <span class="modern-badge modern-badge-secondary">
                      {{ companyUser.companyID || 'N/A' }}
                    </span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar-circle" [style.background-color]="getAvatarColor(companyUser.name)">
                        {{ getInitials(companyUser.name) }}
                      </div>
                      <div class="ms-3">{{ companyUser.name }}</div>
                    </div>
                  </td>
                  <td>{{ companyUser.email }}</td>
                  <td>{{ companyUser.phoneNumber }}</td>
                  <td>
                    <div *ngIf="companyUser.companyName; else noCompany">
                      {{ companyUser.companyName }}
                    </div>
                    <ng-template #noCompany>
                      <span class="text-muted">Atanmamış</span>
                    </ng-template>
                  </td>
                  <td>
                    <span class="status-badge" [ngClass]="getStatusClass(companyUser)">
                      {{ getStatusText(companyUser) }}
                    </span>
                  </td>
                  <td>
                    <div class="d-flex justify-content-center gap-2">
                      <button
                        class="btn-modern btn-modern-info btn-modern-icon btn-modern-icon-sm"
                        title="Detaylar"
                        (click)="viewCompanyUserDetails(companyUser)"
                      >
                        <i class="fas fa-info"></i>
                      </button>
                      <button
                        class="btn-modern btn-modern-primary btn-modern-icon btn-modern-icon-sm"
                        title="Düzenle"
                        (click)="editCompanyUser(companyUser)"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Card View -->
          <div *ngIf="viewMode === 'card'">
            <div class="row">
              <div class="col-md-4 col-lg-3 mb-4" *ngFor="let companyUser of companyUsers">
                <div class="modern-card h-100 staggered-item">
                  <div class="card-body text-center">
                    <div class="avatar-circle-lg mx-auto mb-3" [style.background-color]="getAvatarColor(companyUser.name)">
                      {{ getInitials(companyUser.name) }}
                    </div>
                    <h5 class="mb-1">{{ companyUser.name }}</h5>
                    <p class="text-muted mb-1">{{ companyUser.email }}</p>
                    <p class="text-muted mb-1">{{ companyUser.phoneNumber }}</p>
                    <div class="mb-2">
                      <span class="modern-badge modern-badge-secondary">
                        Şirket ID: {{ companyUser.companyID || 'N/A' }}
                      </span>
                    </div>
                    <div class="mb-3">
                      <span class="status-badge" [ngClass]="getStatusClass(companyUser)">
                        {{ getStatusText(companyUser) }}
                      </span>
                    </div>
                    <div class="d-flex justify-content-center gap-2">
                      <button
                        class="btn-modern btn-modern-info btn-modern-icon"
                        title="Detaylar"
                        (click)="viewCompanyUserDetails(companyUser)"
                      >
                        <i class="fas fa-info"></i>
                      </button>
                      <button
                        class="btn-modern btn-modern-primary btn-modern-icon"
                        title="Düzenle"
                        (click)="editCompanyUser(companyUser)"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <div class="d-flex justify-content-between align-items-center mt-4" *ngIf="totalPages > 1">
            <div class="pagination-info">
              <span class="text-muted">
                Toplam {{ totalItems }} kayıttan {{ companyUsers.length }} tanesi gösteriliyor
              </span>
            </div>
            <nav>
              <ul class="modern-pagination">
                <li [class.disabled]="currentPage === 1">
                  <button (click)="onPageChange(currentPage - 1)" [disabled]="currentPage === 1">
                    <i class="fas fa-chevron-left"></i>
                  </button>
                </li>
                <li *ngFor="let page of [].constructor(totalPages); let i = index" [class.active]="currentPage === i + 1">
                  <button (click)="onPageChange(i + 1)">{{ i + 1 }}</button>
                </li>
                <li [class.disabled]="currentPage === totalPages">
                  <button (click)="onPageChange(currentPage + 1)" [disabled]="currentPage === totalPages">
                    <i class="fas fa-chevron-right"></i>
                  </button>
                </li>
              </ul>
            </nav>
          </div>

          <!-- Empty State -->
          <div class="text-center py-5" *ngIf="!isLoading && companyUsers.length === 0">
            <div class="empty-state-icon mb-3">
              <i class="fas fa-building fa-3x text-muted"></i>
            </div>
            <h5 class="text-muted">Şirket kullanıcısı bulunamadı</h5>
            <p class="text-muted">
              {{ searchText ? 'Arama kriterlerinize uygun kullanıcı bulunamadı.' : 'Henüz hiç şirket kullanıcısı eklenmemiş.' }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
