import { Component, OnInit, OnDestroy } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Subject } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';
import { 
  faTrashRestore, 
  faInfoCircle, 
  faCalendarAlt, 
  faUser, 
  faExclamationTriangle,
  faCheckCircle,
  faTimesCircle,
  faFilter,
  faDownload
} from '@fortawesome/free-solid-svg-icons';

import { DeletedCompanyUserService } from '../../services/deleted-company-user.service';
import { DeletedCompanyUser } from '../../models/deleted-company-user';
import { DeletedCompanyUserDetailDialogComponent } from '../deleted-company-user-detail-dialog/deleted-company-user-detail-dialog.component';
import { CompanyUserRestoreDialogComponent } from '../company-user-restore-dialog/company-user-restore-dialog.component';

@Component({
  selector: 'app-deleted-company-users',
  templateUrl: './deleted-company-users.component.html',
  styleUrls: ['./deleted-company-users.component.css'],
  standalone: false
})
export class DeletedCompanyUsersComponent implements OnInit, OnDestroy {
  deletedCompanyUsers: DeletedCompanyUser[] = [];
  filteredCompanyUsers: DeletedCompanyUser[] = [];
  isLoading = false;
  searchText = '';
  selectedFilter = 'all'; // all, deleted, restored
  selectedDateRange: { start: Date | null, end: Date | null } = { start: null, end: null };

  // Icons
  faTrashRestore = faTrashRestore;
  faInfoCircle = faInfoCircle;
  faCalendarAlt = faCalendarAlt;
  faUser = faUser;
  faExclamationTriangle = faExclamationTriangle;
  faCheckCircle = faCheckCircle;
  faTimesCircle = faTimesCircle;
  faFilter = faFilter;
  faDownload = faDownload;

  // Statistics
  totalDeleted = 0;
  totalRestored = 0;
  canBeRestoredCount = 0;

  private destroy$ = new Subject<void>();
  private searchSubject = new Subject<string>();

  constructor(
    private deletedCompanyUserService: DeletedCompanyUserService,
    private dialog: MatDialog,
    private toastrService: ToastrService
  ) {
    // Search debounce
    this.searchSubject.pipe(
      debounceTime(300),
      takeUntil(this.destroy$)
    ).subscribe(searchValue => {
      this.searchText = searchValue;
      this.applyFilters();
    });
  }

  ngOnInit(): void {
    this.loadDeletedCompanyUsers();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadDeletedCompanyUsers(): void {
    this.isLoading = true;

    this.deletedCompanyUserService.getDeletedCompanyUsers().subscribe({
      next: (response) => {
        if (response.success) {
          this.deletedCompanyUsers = response.data;
          this.calculateStatistics();
          this.applyFilters();
        } else {
          this.toastrService.error(response.message || 'Silinen salonlar yüklenirken bir hata oluştu.', 'Hata');
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading deleted company users:', error);
        this.toastrService.error('Silinen salonlar yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    });
  }

  onSearch(event: any): void {
    const searchValue = event.target.value;
    this.searchSubject.next(searchValue);
  }

  onFilterChange(filter: string): void {
    this.selectedFilter = filter;
    this.applyFilters();
  }

  onDateRangeChange(): void {
    this.applyFilters();
  }

  applyFilters(): void {
    let filtered = [...this.deletedCompanyUsers];

    // Text search
    if (this.searchText) {
      const searchLower = this.searchText.toLowerCase();
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower) ||
        user.companyName.toLowerCase().includes(searchLower) ||
        user.deletedByUser.toLowerCase().includes(searchLower)
      );
    }

    // Status filter
    switch (this.selectedFilter) {
      case 'deleted':
        filtered = filtered.filter(user => !user.isRestored);
        break;
      case 'restored':
        filtered = filtered.filter(user => user.isRestored);
        break;
      case 'restorable':
        filtered = filtered.filter(user => user.canBeRestored && !user.isRestored);
        break;
    }

    // Date range filter
    if (this.selectedDateRange.start && this.selectedDateRange.end) {
      filtered = filtered.filter(user => {
        const deletedDate = new Date(user.deletedDate!);
        return deletedDate >= this.selectedDateRange.start! && 
               deletedDate <= this.selectedDateRange.end!;
      });
    }

    this.filteredCompanyUsers = filtered;
  }

  calculateStatistics(): void {
    this.totalDeleted = this.deletedCompanyUsers.length;
    this.totalRestored = this.deletedCompanyUsers.filter(u => u.isRestored).length;
    this.canBeRestoredCount = this.deletedCompanyUsers.filter(u => u.canBeRestored && !u.isRestored).length;
  }

  viewDetails(deletedCompanyUser: DeletedCompanyUser): void {
    const dialogRef = this.dialog.open(DeletedCompanyUserDetailDialogComponent, {
      width: '95%',
      maxWidth: '1200px',
      height: '85vh',
      data: { companyUserId: deletedCompanyUser.companyUserID },
      panelClass: 'deleted-company-user-detail-dialog'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'restored') {
        this.loadDeletedCompanyUsers();
      }
    });
  }

  restoreCompanyUser(deletedCompanyUser: DeletedCompanyUser): void {
    if (!deletedCompanyUser.canBeRestored) {
      this.toastrService.warning(
        deletedCompanyUser.restoreBlockReason || 'Bu salon geri getirilemez.',
        'Uyarı'
      );
      return;
    }

    const dialogRef = this.dialog.open(CompanyUserRestoreDialogComponent, {
      width: '800px',
      maxHeight: '90vh',
      data: { 
        companyUserId: deletedCompanyUser.companyUserID,
        companyUserName: deletedCompanyUser.name
      },
      panelClass: 'company-user-restore-dialog'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'restored') {
        this.toastrService.success('Salon başarıyla geri getirildi.', 'Başarılı');
        this.loadDeletedCompanyUsers();
      }
    });
  }

  getStatusClass(deletedCompanyUser: DeletedCompanyUser): string {
    if (deletedCompanyUser.isRestored) {
      return 'status-restored';
    } else if (deletedCompanyUser.canBeRestored) {
      return 'status-restorable';
    } else {
      return 'status-deleted';
    }
  }

  getStatusText(deletedCompanyUser: DeletedCompanyUser): string {
    if (deletedCompanyUser.isRestored) {
      return 'Geri Getirildi';
    } else if (deletedCompanyUser.canBeRestored) {
      return 'Geri Getirilebilir';
    } else {
      return 'Silinmiş';
    }
  }

  getStatusIcon(deletedCompanyUser: DeletedCompanyUser): any {
    if (deletedCompanyUser.isRestored) {
      return faCheckCircle;
    } else if (deletedCompanyUser.canBeRestored) {
      return faTrashRestore;
    } else {
      return faTimesCircle;
    }
  }

  formatDate(date: Date | undefined): string {
    if (!date) return 'Bilinmiyor';
    return new Date(date).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  exportToExcel(): void {
    // Excel export functionality
    this.toastrService.info('Excel export özelliği yakında eklenecek.', 'Bilgi');
  }

  clearFilters(): void {
    this.searchText = '';
    this.selectedFilter = 'all';
    this.selectedDateRange = { start: null, end: null };
    this.applyFilters();
  }
}
