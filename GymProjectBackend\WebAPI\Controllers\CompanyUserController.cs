using Business.Abstract;
using Core.Entities.Concrete;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CompanyUserController : ControllerBase
    {
        ICompanyUserService _companyUserService;
        public CompanyUserController(ICompanyUserService companyUserService)
        {
            _companyUserService = companyUserService;
            
        }
        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _companyUserService.GetAll();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }
        [HttpPost("add")]
        public IActionResult Add(CompanyUser companyUser)
        {
            var result = _companyUserService.Add(companyUser);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }
       
        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _companyUserService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }
        [HttpPost("update")]
        public IActionResult Update(CompanyUser companyUser)
        {
            var result = _companyUserService.Update(companyUser);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }
        [HttpGet("getcompanydetails")]
        public IActionResult Get()
        {
            var result = _companyUserService.GetCompanyDetails();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getbycityid")]

        public IActionResult GetByCityId(int id)
        {
            var result = _companyUserService.GetByCityId(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getcompanyuserdetailsbycityid")]
        public IActionResult GetCompanyUserDetailsByCityId(int cityId)
        {
            var result = _companyUserService.GetCompanyUserDetailsByCityId(cityId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getcompanyuserdetails")]
        public IActionResult GetCompanyUserDetails()
        {
            var result = _companyUserService.GetCompanyUserDetails();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        // Yeni eklenen endpoint'ler
        [HttpGet("getbyid")]
        public IActionResult GetById(int id)
        {
            var result = _companyUserService.GetById(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getfulldetails")]
        public IActionResult GetCompanyUserFullDetails(int id)
        {
            var result = _companyUserService.GetCompanyUserFullDetails(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getpaginated")]
        public IActionResult GetCompanyUsersPaginated(int pageNumber = 1, int pageSize = 10, string searchTerm = "")
        {
            var result = _companyUserService.GetCompanyUsersPaginated(pageNumber, pageSize, searchTerm);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPost("updatefull")]
        public IActionResult UpdateCompanyUserFull(CompanyUserFullUpdateDto updateDto)
        {
            var result = _companyUserService.UpdateCompanyUserFull(updateDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
