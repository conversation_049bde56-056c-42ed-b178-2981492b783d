
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: '6377bea102f6bc7bb59f8bee1a96572f5b2a0f60cdfdfd0a9c666dc0fd1ac570', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '5b8658dd3519804993988ca31303535c44304cd987e4f45ad75eb70be17271e4', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-TBXNKKXU.css': {size: 298105, hash: '7i4JWBKmeRM', text: () => import('./assets-chunks/styles-TBXNKKXU_css.mjs').then(m => m.default)}
  },
};
