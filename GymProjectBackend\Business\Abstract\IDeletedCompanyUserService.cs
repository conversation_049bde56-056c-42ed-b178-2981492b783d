using Core.Utilities.Results;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Business.Abstract
{
    /// <summary>
    /// Silinen salonlar yönetimi için servis interface'i
    /// </summary>
    public interface IDeletedCompanyUserService
    {
        /// <summary>
        /// Silinen tüm salonları getirir
        /// </summary>
        Task<IDataResult<List<DeletedCompanyUserDto>>> GetDeletedCompanyUsersAsync();
        
        /// <summary>
        /// Belirli bir silinen salonun detaylarını getirir
        /// </summary>
        Task<IDataResult<DeletedCompanyUserDto>> GetDeletedCompanyUserByIdAsync(int companyUserId);
        
        /// <summary>
        /// Silinen salon için deletion snapshot'ını getirir
        /// </summary>
        Task<IDataResult<CompanyUserDeletionSnapshotDto>> GetDeletionSnapshotAsync(int companyUserId);
        
        /// <summary>
        /// Salon geri getirme işlemini gerçekleştirir
        /// </summary>
        Task<IDataResult<CompanyUserRestoreResultDto>> RestoreCompanyUserAsync(CompanyUserRestoreDto restoreDto);
        
        /// <summary>
        /// Geri getirme öncesi çakışma analizi yapar
        /// </summary>
        Task<IDataResult<List<ConflictInfo>>> AnalyzeRestoreConflictsAsync(int companyUserId);
        
        /// <summary>
        /// Silinen salonları tarih aralığına göre filtreler
        /// </summary>
        Task<IDataResult<List<DeletedCompanyUserDto>>> GetDeletedCompanyUsersByDateRangeAsync(
            DateTime startDate, DateTime endDate);
        
        /// <summary>
        /// Belirli bir kullanıcının sildiği salonları getirir
        /// </summary>
        Task<IDataResult<List<DeletedCompanyUserDto>>> GetDeletedCompanyUsersByUserAsync(int userId);
        
        /// <summary>
        /// Geri getirilen salonları getirir
        /// </summary>
        Task<IDataResult<List<DeletedCompanyUserDto>>> GetRestoredCompanyUsersAsync();
        
        /// <summary>
        /// Silinen salon için deletion snapshot oluşturur (silme işlemi sırasında çağrılır)
        /// </summary>
        Task<IResult> CreateDeletionSnapshotAsync(int companyUserId, string deletionReason, int deletedByUserId);
        
        /// <summary>
        /// Kalıcı silme işlemi (dikkatli kullanılmalı)
        /// </summary>
        Task<IResult> PermanentDeleteAsync(int companyUserId, string reason);
        
        /// <summary>
        /// Toplu geri getirme işlemi
        /// </summary>
        Task<IDataResult<List<CompanyUserRestoreResultDto>>> BulkRestoreAsync(
            List<int> companyUserIds, RestoreOptions options);
    }
}
