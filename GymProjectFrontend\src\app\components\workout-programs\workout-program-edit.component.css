/* Workout Program Edit Specific Styles */

/* Sticky Header */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1020;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed) var(--transition-timing);
}

[data-theme="dark"] .sticky-header {
  background-color: var(--bg-primary);
  border-bottom-color: var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.main-content {
  padding-top: 0;
}

.day-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background-color: var(--bg-primary);
  transition: all var(--transition-speed) var(--transition-timing);
}

.day-card:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.day-header {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.day-body {
  padding: 1.25rem;
}

.day-number {
  font-weight: 600;
  color: var(--primary);
}

.day-name-preview {
  font-weight: 500;
  color: var(--text-secondary);
}

.exercise-summary {
  padding: 0.75rem 1rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-input:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.25rem var(--primary-light);
}

.form-check-label {
  font-weight: 500;
  margin-left: 0.5rem;
}

.is-invalid {
  border-color: var(--danger);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--danger);
}

.alert {
  padding: 0.75rem 1rem;
  margin-bottom: 0;
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
}

.alert-warning {
  color: var(--warning);
  background-color: var(--warning-light);
  border-color: rgba(var(--warning-rgb), 0.2);
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sticky-header .modern-card-header {
    padding: 0.75rem 1rem;
  }

  .sticky-header .modern-card-header .d-flex.gap-2 {
    flex-direction: row;
    gap: 0.25rem !important;
  }

  .sticky-header .modern-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }

  .sticky-header h4 {
    font-size: 1.1rem;
  }

  .day-header {
    padding: 0.75rem 1rem;
  }

  .day-body {
    padding: 1rem;
  }

  .modern-btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .d-flex.gap-2:not(.sticky-header .d-flex.gap-2) {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .day-name-preview {
    display: block;
    margin-top: 0.25rem;
  }
}

/* Dark mode specific adjustments */
[data-theme="dark"] .day-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .day-header {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .exercise-summary {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .form-check-input {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

[data-theme="dark"] .alert-warning {
  background-color: var(--warning-light);
  border-color: rgba(var(--warning-rgb), 0.3);
  color: var(--warning);
}
