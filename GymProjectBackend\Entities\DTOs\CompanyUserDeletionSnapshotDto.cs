using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Salon silme işlemi sırasında oluşturulan snapshot bilgileri
    /// Geri getirme işlemi için kullanılır
    /// </summary>
    public class CompanyUserDeletionSnapshotDto : IDto
    {
        public int CompanyUserID { get; set; }
        public DateTime DeletionDate { get; set; }
        public string DeletionReason { get; set; }
        public int DeletedByUserId { get; set; }
        public string DeletedByUserName { get; set; }
        
        // Silinen veri sayıları
        public DeletionStatistics Statistics { get; set; } = new DeletionStatistics();
        
        // Geri getirme için gerekli ID'ler
        public List<int> DeletedMemberIds { get; set; } = new List<int>();
        public List<int> DeletedMembershipIds { get; set; } = new List<int>();
        public List<int> DeletedPaymentIds { get; set; } = new List<int>();
        public List<int> DeletedTransactionIds { get; set; } = new List<int>();
        public List<int> DeletedEntryExitIds { get; set; } = new List<int>();
        public List<int> DeletedDebtPaymentIds { get; set; } = new List<int>();
        public List<int> DeletedCompanyExerciseIds { get; set; } = new List<int>();
        public List<int> DeletedWorkoutProgramIds { get; set; } = new List<int>();
        
        // Şirket bilgileri
        public int? CompanyId { get; set; }
        public int? CompanyAddressId { get; set; }
        public int? UserCompanyId { get; set; }
        public int? UserId { get; set; }
        
        // Çakışma kontrolü için
        public List<ConflictInfo> PotentialConflicts { get; set; } = new List<ConflictInfo>();
    }
    
    public class DeletionStatistics
    {
        public int TotalMembers { get; set; }
        public int TotalMemberships { get; set; }
        public int TotalPayments { get; set; }
        public int TotalTransactions { get; set; }
        public int TotalEntryExits { get; set; }
        public int TotalDebtPayments { get; set; }
        public int TotalExercises { get; set; }
        public int TotalWorkoutPrograms { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal UnpaidAmount { get; set; }
    }
    
    public class ConflictInfo
    {
        public string EntityType { get; set; }
        public string ConflictType { get; set; }
        public string Description { get; set; }
        public string Resolution { get; set; }
        public bool IsBlocker { get; set; }
    }
}
