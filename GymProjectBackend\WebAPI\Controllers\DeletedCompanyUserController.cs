using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DeletedCompanyUserController : ControllerBase
    {
        private readonly IDeletedCompanyUserService _deletedCompanyUserService;

        public DeletedCompanyUserController(IDeletedCompanyUserService deletedCompanyUserService)
        {
            _deletedCompanyUserService = deletedCompanyUserService;
        }

        /// <summary>
        /// Silinen tüm salonları getirir
        /// </summary>
        [HttpGet("getall")]
        public async Task<IActionResult> GetDeletedCompanyUsers()
        {
            var result = await _deletedCompanyUserService.GetDeletedCompanyUsersAsync();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// <PERSON>irli bir silinen salonun detaylarını getirir
        /// </summary>
        [HttpGet("getbyid")]
        public async Task<IActionResult> GetDeletedCompanyUserById(int companyUserId)
        {
            var result = await _deletedCompanyUserService.GetDeletedCompanyUserByIdAsync(companyUserId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Silinen salon için deletion snapshot'ını getirir
        /// </summary>
        [HttpGet("getsnapshot")]
        public async Task<IActionResult> GetDeletionSnapshot(int companyUserId)
        {
            var result = await _deletedCompanyUserService.GetDeletionSnapshotAsync(companyUserId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Geri getirme öncesi çakışma analizi yapar
        /// </summary>
        [HttpGet("analyzeconflicts")]
        public async Task<IActionResult> AnalyzeRestoreConflicts(int companyUserId)
        {
            var result = await _deletedCompanyUserService.AnalyzeRestoreConflictsAsync(companyUserId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Salon geri getirme işlemini gerçekleştirir
        /// </summary>
        [HttpPost("restore")]
        public async Task<IActionResult> RestoreCompanyUser([FromBody] CompanyUserRestoreDto restoreDto)
        {
            var result = await _deletedCompanyUserService.RestoreCompanyUserAsync(restoreDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Silinen salonları tarih aralığına göre filtreler
        /// </summary>
        [HttpGet("getbydaterange")]
        public async Task<IActionResult> GetDeletedCompanyUsersByDateRange(
            [FromQuery] DateTime startDate, 
            [FromQuery] DateTime endDate)
        {
            var result = await _deletedCompanyUserService.GetDeletedCompanyUsersByDateRangeAsync(startDate, endDate);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Belirli bir kullanıcının sildiği salonları getirir
        /// </summary>
        [HttpGet("getbyuser")]
        public async Task<IActionResult> GetDeletedCompanyUsersByUser(int userId)
        {
            var result = await _deletedCompanyUserService.GetDeletedCompanyUsersByUserAsync(userId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Geri getirilen salonları getirir
        /// </summary>
        [HttpGet("getrestored")]
        public async Task<IActionResult> GetRestoredCompanyUsers()
        {
            var result = await _deletedCompanyUserService.GetRestoredCompanyUsersAsync();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Toplu geri getirme işlemi
        /// </summary>
        [HttpPost("bulkrestore")]
        public async Task<IActionResult> BulkRestore([FromBody] BulkRestoreRequestDto request)
        {
            var result = await _deletedCompanyUserService.BulkRestoreAsync(request.CompanyUserIds, request.Options);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Kalıcı silme işlemi (dikkatli kullanılmalı)
        /// </summary>
        [HttpDelete("permanentdelete")]
        public async Task<IActionResult> PermanentDelete(int companyUserId, [FromBody] string reason)
        {
            var result = await _deletedCompanyUserService.PermanentDeleteAsync(companyUserId, reason);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }

    /// <summary>
    /// Toplu geri getirme isteği için DTO
    /// </summary>
    public class BulkRestoreRequestDto
    {
        public List<int> CompanyUserIds { get; set; } = new List<int>();
        public RestoreOptions Options { get; set; } = new RestoreOptions();
    }
}
